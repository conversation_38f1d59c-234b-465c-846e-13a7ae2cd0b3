# ComfyUI Video Chain

A Node.js tool for creating video sequences using ComfyUI's image-to-video capabilities.

## Project Structure

```
comfy_video_chain/
├── chain_i2v.js              # Main script
├── wan2.2_chain_base.json    # Base ComfyUI workflow (essential)
├── package.json              # Node.js dependencies
├── config/                   # User configuration files (gitignored)
│   ├── example.json          # Example configuration
│   └── *.json               # Your config files
├── output/                   # Final video outputs (gitignored)
├── runs/                     # Temporary segment files (gitignored)
└── README.md                # This file
```

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create your configuration file in the `config/` folder based on `config/example.json`

3. Make sure you have ComfyUI running and accessible

## Usage

```bash
node chain_i2v.js ./config/your_config.json
```

## Configuration

Create a JSON configuration file in the `config/` folder with the following structure:

```json
{
  "image": "./start.png",
  "width": 464,
  "height": 624,
  "fps": 24,
  "prompt_chain": [
    {
      "positive_prompt": "your prompt here",
      "negative_prompt": "blurry, low quality, text, watermark",
      "length": 3
    }
  ],
  "output": "./output/final_video.mp4",
  "segment_prefix": "seg"
}
```

## Files and Folders

- **wan2.2_chain_base.json**: Essential ComfyUI workflow file (tracked in git)
- **config/**: Contains user configuration files (not tracked in git for privacy)
- **output/**: Contains final rendered videos (not tracked in git due to file size)
- **runs/**: Contains temporary segment files during processing (not tracked in git)

## Git

The project is set up with a comprehensive `.gitignore` that excludes:
- User configuration files (may contain sensitive prompts)
- Output videos (large files)
- Temporary run files
- Node modules and other standard exclusions

The essential base workflow file (`wan2.2_chain_base.json`) and source code are tracked in git.
